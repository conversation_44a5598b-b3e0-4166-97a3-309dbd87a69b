const { User, Exchange, Announcement, Log, Feedback, Notification, Product, sequelize, Workplace } = require('../models');
const { ACTIONS, ENTITY_TYPES } = require('../models/log');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const multer = require('multer');
const { Op } = require('sequelize');

// 确保支付码目录存在
const paymentQrcodeDir = path.join(__dirname, '..', 'uploads', 'payment');
if (!fs.existsSync(paymentQrcodeDir)) {
  fs.mkdirSync(paymentQrcodeDir, { recursive: true });
  console.log('创建支付码目录:', paymentQrcodeDir);
}

// 确保config目录存在
const configDir = path.join(__dirname, '..', 'config');
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
  console.log('创建配置目录:', configDir);
}

// 支付码JSON配置文件路径
const qrcodeConfigPath = path.join(__dirname, '..', 'config', 'payment_qrcode.json');

// 如果配置文件不存在，创建一个空的配置文件
if (!fs.existsSync(qrcodeConfigPath)) {
  fs.writeFileSync(qrcodeConfigPath, JSON.stringify({
    filename: null,
    uploadTime: null
  }), 'utf8');
  console.log('创建空的支付码配置文件:', qrcodeConfigPath);
}

/**
 * 获取支付收款码信息
 */
exports.getPaymentQRCode = async (req, res) => {
  try {
    console.log('正在获取支付收款码信息...');
    
    // 检查配置文件是否存在
    if (!fs.existsSync(qrcodeConfigPath)) {
      console.log('配置文件不存在，创建默认配置');
      return res.status(200).json({
        qrcodeUrl: null,
        uploadTime: null,
        message: '未设置支付收款码'
      });
    }
    
    // 读取配置
    const qrcodeConfig = JSON.parse(fs.readFileSync(qrcodeConfigPath, 'utf8'));
    console.log('读取到的收款码配置:', qrcodeConfig);
    
    // 检查图片文件是否存在
    if (qrcodeConfig.filename) {
      const qrcodeFilePath = path.join(paymentQrcodeDir, qrcodeConfig.filename);
      const fileExists = fs.existsSync(qrcodeFilePath);
      console.log(`检查收款码文件 ${qrcodeConfig.filename} 是否存在:`, fileExists);
      
      if (fileExists) {
        // 检查文件大小，确保不是空文件
        const stats = fs.statSync(qrcodeFilePath);
        console.log(`收款码文件大小: ${stats.size} 字节`);
        
        if (stats.size === 0) {
          console.error('收款码文件存在但为空');
          return res.status(200).json({
            qrcodeUrl: null,
            uploadTime: null,
            message: '支付收款码文件为空，请重新上传'
          });
        }
        
        // 构建完整URL
        const qrcodeUrl = getQrcodeUrl(req, qrcodeConfig.filename);
        console.log('生成的收款码URL:', qrcodeUrl);
        
        return res.status(200).json({
          qrcodeUrl,
          uploadTime: qrcodeConfig.uploadTime,
          filename: qrcodeConfig.filename
        });
      } else {
        console.error('配置中的收款码文件不存在:', qrcodeConfig.filename);
        
        // 图片文件不存在，更新配置
        fs.writeFileSync(qrcodeConfigPath, JSON.stringify({
          filename: null,
          uploadTime: null
        }), 'utf8');
        
        return res.status(200).json({
          qrcodeUrl: null,
          uploadTime: null,
          message: '支付收款码文件不存在，请重新上传'
        });
      }
    } else {
      console.log('配置中未设置收款码文件名');
      return res.status(200).json({
        qrcodeUrl: null,
        uploadTime: null,
        message: '未设置支付收款码'
      });
    }
  } catch (error) {
    console.error('获取支付收款码失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取支付收款码失败',
      error: error.message
    });
  }
};

/**
 * 上传支付收款码
 */
exports.uploadPaymentQRCode = async (req, res) => {
  console.log('开始处理支付收款码上传请求...');
  
  // 确保上传目录存在
  if (!fs.existsSync(paymentQrcodeDir)) {
    console.log(`创建支付码目录: ${paymentQrcodeDir}`);
    fs.mkdirSync(paymentQrcodeDir, { recursive: true });
  }
  
  // 配置multer存储
  const storage = multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, paymentQrcodeDir);
    },
    filename: (req, file, cb) => {
      // 创建唯一文件名，保留原始扩展名
      const ext = path.extname(file.originalname) || '.png';
      const uniqueFilename = `qrcode_${uuidv4()}${ext}`;
      console.log(`生成唯一文件名: ${uniqueFilename}`);
      cb(null, uniqueFilename);
    }
  });
  
  // 配置文件过滤器
  const fileFilter = (req, file, cb) => {
    console.log('收到文件:', file.originalname, '类型:', file.mimetype);
    // 仅接受图片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      const error = new Error('仅支持上传图片文件');
      console.error(error.message);
      cb(error);
    }
  };
  
  // 创建multer实例
  const upload = multer({
    storage,
    limits: { fileSize: 2 * 1024 * 1024 }, // 2MB限制
    fileFilter
  }).single('file');
  
  // 处理上传
  upload(req, res, async (err) => {
    if (err) {
      console.error('上传支付收款码错误:', err);
      return res.status(400).json({
        success: false,
        message: err.message || '上传支付收款码失败'
      });
    }
    
    if (!req.file) {
      console.error('未收到文件');
      return res.status(400).json({
        success: false,
        message: '未收到文件'
      });
    }
    
    try {
      console.log('文件上传成功:', req.file);
      
      // 验证文件大小
      const stats = fs.statSync(req.file.path);
      if (stats.size === 0) {
        console.error('上传的文件为空');
        fs.unlinkSync(req.file.path);
        return res.status(400).json({
          success: false,
          message: '上传的文件为空'
        });
      }
      
      // 构建URL
      const qrcodeUrl = getQrcodeUrl(req, req.file.filename);
      console.log('生成的收款码URL:', qrcodeUrl);
      
      // 删除之前的图片（如果存在）
      const oldDeleted = await deleteOldQrcode();
      console.log('删除旧收款码结果:', oldDeleted);
      
      // 保存新的配置
      const config = {
        filename: req.file.filename,
        uploadTime: new Date().toISOString()
      };
      
      fs.writeFileSync(qrcodeConfigPath, JSON.stringify(config), 'utf8');
      console.log('已更新收款码配置:', config);
      
      // 记录日志
      await createQrcodeLog(req, 'upload_qrcode', `管理员 ${req.user.username} 上传了新的支付收款码`);
      
      return res.status(200).json({
        success: true,
        message: '支付收款码上传成功',
        qrcodeUrl,
        uploadTime: config.uploadTime,
        filename: config.filename
      });
    } catch (error) {
      console.error('处理支付收款码上传错误:', error);
      // 上传失败，删除刚刚上传的文件
      try {
        if (req.file && req.file.path) {
          fs.unlinkSync(req.file.path);
          console.log('已删除临时上传文件:', req.file.path);
        }
      } catch (unlinkError) {
        console.error('删除临时文件失败:', unlinkError);
      }
      
      return res.status(500).json({
        success: false,
        message: '处理支付收款码上传失败',
        error: error.message
      });
    }
  });
};

/**
 * 删除支付收款码
 */
exports.deletePaymentQRCode = async (req, res) => {
  try {
    // 删除之前的图片（如果存在）
    const deleted = await deleteOldQrcode();
    
    // 清空配置
    fs.writeFileSync(qrcodeConfigPath, JSON.stringify({
      filename: null,
      uploadTime: null
    }), 'utf8');
    
    // 记录日志
    await createQrcodeLog(req, 'delete_qrcode', `管理员 ${req.user.username} 删除了支付收款码`);
    
    return res.status(200).json({
      success: true,
      message: deleted ? '支付收款码已删除' : '没有支付收款码需要删除',
    });
  } catch (error) {
    console.error('删除支付收款码失败:', error);
    return res.status(500).json({
      success: false,
      message: '删除支付收款码失败',
      error: error.message
    });
  }
};

/**
 * 辅助函数 - 删除旧的收款码图片
 */
async function deleteOldQrcode() {
  try {
    console.log('尝试删除旧的收款码文件...');
    
    // 检查配置文件是否存在
    if (!fs.existsSync(qrcodeConfigPath)) {
      console.log('配置文件不存在，无需删除旧文件');
      return false;
    }
    
    // 读取配置
    const qrcodeConfig = JSON.parse(fs.readFileSync(qrcodeConfigPath, 'utf8'));
    console.log('读取到的收款码配置:', qrcodeConfig);
    
    // 如果有旧图片，删除它
    if (qrcodeConfig.filename) {
      const oldFilePath = path.join(paymentQrcodeDir, qrcodeConfig.filename);
      
      if (fs.existsSync(oldFilePath)) {
        console.log(`删除旧文件: ${oldFilePath}`);
        fs.unlinkSync(oldFilePath);
        return true;
      } else {
        console.log(`旧文件不存在: ${oldFilePath}`);
      }
    } else {
      console.log('配置中没有文件名，无需删除旧文件');
    }
    
    return false;
  } catch (error) {
    console.error('删除旧收款码失败:', error);
    return false;
  }
}

/**
 * 辅助函数 - 获取收款码完整URL
 */
function getQrcodeUrl(req, filename) {
  if (!filename) {
    console.error('生成URL时filename为空');
    return null;
  }

  // 获取环境信息
  const nodeEnv = process.env.NODE_ENV || 'development';
  const isLocalDevelopment = nodeEnv === 'development';
  
  console.log(`getQrcodeUrl: 环境=${nodeEnv}, 本地开发=${isLocalDevelopment}`);
  
  // 确定基础URL
  let baseUrl;
  
  // 在本地开发环境中强制使用localhost
  if (isLocalDevelopment) {
    baseUrl = 'http://localhost:3000';
    console.log('本地开发环境: 强制使用localhost URL');
  } else {
    // 生产环境使用SERVER_URL或从请求中获取
    baseUrl = process.env.SERVER_URL;
    
    // 生产环境但未设置SERVER_URL，从请求中获取
    if (!baseUrl) {
      const protocol = req.headers['x-forwarded-proto'] || req.protocol;
      const host = req.headers['x-forwarded-host'] || req.get('host');
      baseUrl = `${protocol}://${host}`;
    }
    
    // 确保生产环境使用正确的URL
    if (!isLocalDevelopment && baseUrl.includes('localhost')) {
      console.log('⚠️ 生产环境使用了localhost，修正为生产URL');
      baseUrl = 'http://**************:3000';
    }
  }
  
  console.log(`使用基础URL: ${baseUrl}，当前环境: ${nodeEnv}`);
  
  // 构建URL路径，确保路径格式正确
  const urlPath = `/uploads/payment/${encodeURIComponent(filename)}`;
  const fullUrl = `${baseUrl}${urlPath}`;
  
  console.log(`生成的完整URL: ${fullUrl}`);
  return fullUrl;
}

/**
 * 辅助函数 - 创建收款码操作日志
 */
async function createQrcodeLog(req, action, description) {
  try {
    // 获取IP地址
    let ipAddress = req.headers['x-forwarded-for'] || 
                    req.headers['x-real-ip'] || 
                    req.ip || 
                    req.connection.remoteAddress || 
                    '';
                    
    // 处理IPv6格式
    if (ipAddress.includes('::ffff:')) {
      ipAddress = ipAddress.split('::ffff:')[1];
    }
    
    // 处理本地IPv6地址
    if (ipAddress === '::1') {
      ipAddress = '127.0.0.1';
    }
    
    // 检查用户是否已登录
    if (!req.user) {
      console.log('未登录用户的操作不记录日志:', action);
      return;
    }
    
    // 创建日志记录
    await Log.create({
      action,
      entityType: ENTITY_TYPES.SYSTEM,
      entityId: 0,
      userId: req.user.id,
      username: req.user.username,
      ipAddress,
      description,
      deviceInfo: req.headers['user-agent']
    });
    
    console.log('收款码操作日志已记录:', action);
  } catch (error) {
    console.error('记录收款码操作日志失败:', error);
  }
}

/**
 * 系统重置
 * 删除除管理员外的所有用户、所有订单、所有公告、所有日志，
 * 保留所有商品和分类，并将商品库存统一设置为50
 */
exports.resetSystem = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    console.log('开始系统重置操作...');
    
    // 记录初始数据统计
    const initialStats = await getSystemStats();
    
    // 临时禁用外键检查
    await sequelize.query('SET FOREIGN_KEY_CHECKS = 0', { transaction });
    
    // 1. 删除所有非管理员用户
    const deletedUsersCount = await User.destroy({
      where: { role: 'user' },
      transaction
    });
    console.log(`已删除 ${deletedUsersCount} 个非管理员用户`);
    
    // 2. 删除所有兑换记录
    const deletedExchangesCount = await Exchange.destroy({
      where: {},
      transaction
    });
    console.log(`已删除 ${deletedExchangesCount} 条兑换记录`);
    
    // 3. 删除所有公告
    const deletedAnnouncementsCount = await Announcement.destroy({
      where: {},
      transaction
    });
    console.log(`已删除 ${deletedAnnouncementsCount} 条公告`);
    
    // 4. 删除所有反馈
    const deletedFeedbacksCount = await Feedback.destroy({
      where: {},
      transaction
    });
    console.log(`已删除 ${deletedFeedbacksCount} 条反馈`);
    
    // 5. 删除所有通知
    const deletedNotificationsCount = await Notification.destroy({
      where: {},
      transaction
    });
    console.log(`已删除 ${deletedNotificationsCount} 条通知`);
    
    // 6. 删除所有日志
    const deletedLogsCount = await Log.destroy({
      where: {},
      transaction
    });
    console.log(`已删除 ${deletedLogsCount} 条日志记录`);
    
    // 7. 更新所有商品库存为50，累计兑换数量为0
    const [updatedProductsCount] = await Product.update(
      { stock: 50, exchangeCount: 0 },
      { where: {}, transaction }
    );
    console.log(`已将 ${updatedProductsCount} 个商品的库存更新为50，累计兑换数量重置为0`);
    
    // 8. 重置各表的自增ID
    // 重置exchanges表的自增ID
    await sequelize.query('ALTER TABLE exchanges AUTO_INCREMENT = 1', { transaction });
    console.log('exchanges表自增ID已重置为1');
    
    // 清理和优化exchanges表，确保重置生效
    await sequelize.query('ANALYZE TABLE exchanges', { transaction });
    console.log('已分析exchanges表结构');
    
    // 重置feedback表的自增ID
    await sequelize.query('ALTER TABLE feedbacks AUTO_INCREMENT = 1', { transaction });
    console.log('feedbacks表自增ID已重置为1');
    
    // 重置notifications表的自增ID
    await sequelize.query('ALTER TABLE notifications AUTO_INCREMENT = 1', { transaction });
    console.log('notifications表自增ID已重置为1');
    
    // 重置logs表的自增ID
    await sequelize.query('ALTER TABLE logs AUTO_INCREMENT = 1', { transaction });
    console.log('logs表自增ID已重置为1');
    
    // 重新启用外键检查
    await sequelize.query('SET FOREIGN_KEY_CHECKS = 1', { transaction });
    
    // 提交事务
    await transaction.commit();
    
    // 获取重置后的系统统计信息
    const finalStats = await getSystemStats();
    
    // 记录系统重置操作日志（在重置后单独创建，因为之前的日志已被删除）
    await createResetLog(req);
    
    // 打印删除统计数据，验证数据类型
    console.log('删除统计数据 (用户):', typeof deletedUsersCount, deletedUsersCount);
    console.log('删除统计数据 (订单):', typeof deletedExchangesCount, deletedExchangesCount);
    console.log('删除统计数据 (公告):', typeof deletedAnnouncementsCount, deletedAnnouncementsCount);
    console.log('删除统计数据 (反馈):', typeof deletedFeedbacksCount, deletedFeedbacksCount);
    console.log('删除统计数据 (通知):', typeof deletedNotificationsCount, deletedNotificationsCount);
    console.log('删除统计数据 (日志):', typeof deletedLogsCount, deletedLogsCount);
    
    // 确保所有统计数据都是数字类型
    const deleteStats = {
      users: Number(deletedUsersCount) || 0,
      exchanges: Number(deletedExchangesCount) || 0,
      announcements: Number(deletedAnnouncementsCount) || 0,
      feedbacks: Number(deletedFeedbacksCount) || 0,
      notifications: Number(deletedNotificationsCount) || 0,
      logs: Number(deletedLogsCount) || 0
    };
    
    console.log('返回给前端的删除统计数据:', deleteStats);
    
    return res.status(200).json({
      success: true,
      message: '系统重置成功',
      stats: {
        initial: initialStats,
        final: finalStats,
        deleted: deleteStats,
        updated: {
          products: Number(updatedProductsCount) || 0
        }
      }
    });
  } catch (error) {
    // 如果出错，回滚事务
    await transaction.rollback();
    
    console.error('系统重置失败:', error);
    return res.status(500).json({
      success: false,
      message: '系统重置失败',
      error: error.message
    });
  }
};

/**
 * 获取系统数据统计
 */
async function getSystemStats() {
  const [
    usersCount,
    adminsCount,
    productsCount,
    categoriesCount,
    exchangesCount,
    announcementsCount,
    feedbacksCount,
    notificationsCount,
    logsCount
  ] = await Promise.all([
    User.count(),
    User.count({ where: { role: 'admin' } }),
    Product.sequelize.models.Product.count(),
    Product.sequelize.models.Category.count(),
    Exchange.count(),
    Announcement.count(),
    Feedback.count(),
    Notification.count(),
    Log.count()
  ]);
  
  return {
    users: {
      total: usersCount,
      admins: adminsCount,
      regular: usersCount - adminsCount
    },
    products: productsCount,
    categories: categoriesCount,
    exchanges: exchangesCount,
    announcements: announcementsCount,
    feedbacks: feedbacksCount,
    notifications: notificationsCount,
    logs: logsCount
  };
}

/**
 * 创建系统重置操作日志
 */
async function createResetLog(req) {
  try {
    // 获取IP地址
    let ipAddress = req.headers['x-forwarded-for'] || 
                    req.headers['x-real-ip'] || 
                    req.ip || 
                    req.connection.remoteAddress || 
                    '';
                    
    // 处理IPv6格式
    if (ipAddress.includes('::ffff:')) {
      ipAddress = ipAddress.split('::ffff:')[1];
    }
    
    // 处理本地IPv6地址
    if (ipAddress === '::1') {
      ipAddress = '127.0.0.1';
    }
    
    // 创建日志记录
    await Log.create({
      action: 'system_reset',
      entityType: ENTITY_TYPES.SYSTEM,
      entityId: 0,
      userId: req.user.id,
      username: req.user.username,
      ipAddress,
      description: `管理员 ${req.user.username} 执行了系统重置操作`,
      deviceInfo: req.headers['user-agent']
    });
    
    console.log('系统重置日志已记录');
  } catch (error) {
    console.error('记录系统重置日志失败:', error);
  }
}

/**
 * 获取所有职场位置
 */
exports.getWorkplaces = async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';

    // 构建查询条件
    const where = {};
    if (search) {
      where[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { code: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Workplace.findAndCountAll({
      where,
      limit,
      offset,
      order: [['name', 'ASC']]
    });

    return res.json({
      data: rows,
      total: count,
      page,
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('获取职场列表失败:', error);
    return res.status(500).json({ message: '获取职场列表失败，请稍后重试' });
  }
};

/**
 * 获取所有活跃的职场位置（无分页，用于下拉选择）
 */
exports.getAllActiveWorkplaces = async (req, res) => {
  try {
    const workplaces = await Workplace.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']],
      attributes: ['id', 'name', 'code']
    });

    return res.json(workplaces);
  } catch (error) {
    console.error('获取活跃职场列表失败:', error);
    return res.status(500).json({ message: '获取职场列表失败，请稍后重试' });
  }
};

/**
 * 创建新职场
 */
exports.createWorkplace = async (req, res) => {
  try {
    const { name, code, description, isActive } = req.body;

    // 基础验证
    if (!name || !code) {
      return res.status(400).json({ message: '职场名称和代码为必填项' });
    }

    // 检查名称唯一性
    const existingName = await Workplace.findOne({ where: { name } });
    if (existingName) {
      return res.status(409).json({ message: '该职场名称已存在' });
    }

    // 检查代码唯一性
    const existingCode = await Workplace.findOne({ where: { code } });
    if (existingCode) {
      return res.status(409).json({ message: '该职场代码已存在' });
    }

    // 创建职场
    const workplace = await Workplace.create({
      name,
      code,
      description: description || null,
      isActive: isActive !== undefined ? isActive : true
    });

    // 记录日志
    await createLog({
      action: ACTIONS.WORKPLACE_CREATE,
      entityType: ENTITY_TYPES.WORKPLACE,
      entityId: workplace.id,
      userId: req.user.id,
      username: req.user.username,
      description: `管理员创建了职场 "${workplace.name}"`
    }, req);

    return res.status(201).json({
      message: '职场创建成功',
      workplace
    });
  } catch (error) {
    console.error('创建职场失败:', error);
    return res.status(500).json({ message: '创建职场失败，请稍后重试' });
  }
};

/**
 * 更新职场
 */
exports.updateWorkplace = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description, isActive } = req.body;

    const workplace = await Workplace.findByPk(id);
    if (!workplace) {
      return res.status(404).json({ message: '职场不存在' });
    }

    // 检查名称唯一性 (如果已更改)
    if (name !== workplace.name) {
      const existingName = await Workplace.findOne({ where: { name } });
      if (existingName) {
        return res.status(409).json({ message: '该职场名称已存在' });
      }
    }

    // 检查代码唯一性 (如果已更改)
    if (code !== workplace.code) {
      const existingCode = await Workplace.findOne({ where: { code } });
      if (existingCode) {
        return res.status(409).json({ message: '该职场代码已存在' });
      }
    }

    // 保存原始数据用于日志
    const oldData = { ...workplace.get() };

    // 更新职场
    await workplace.update({
      name: name || workplace.name,
      code: code || workplace.code,
      description: description !== undefined ? description : workplace.description,
      isActive: isActive !== undefined ? isActive : workplace.isActive
    });

    // 记录日志
    await createLog({
      action: ACTIONS.WORKPLACE_UPDATE,
      entityType: ENTITY_TYPES.WORKPLACE,
      entityId: workplace.id,
      oldValue: JSON.stringify(oldData),
      newValue: JSON.stringify(workplace.get()),
      userId: req.user.id,
      username: req.user.username,
      description: `管理员更新了职场 "${workplace.name}" 的信息`
    }, req);

    return res.json({
      message: '职场更新成功',
      workplace
    });
  } catch (error) {
    console.error('更新职场失败:', error);
    return res.status(500).json({ message: '更新职场失败，请稍后重试' });
  }
};

/**
 * 删除职场
 */
exports.deleteWorkplace = async (req, res) => {
  try {
    const { id } = req.params;

    const workplace = await Workplace.findByPk(id);
    if (!workplace) {
      return res.status(404).json({ message: '职场不存在' });
    }

    // 检查是否有关联的用户或订单
    const userCount = await User.count({ where: { workplaceId: id } });
    const exchangeCount = await Exchange.count({ where: { workplaceId: id } });

    if (userCount > 0 || exchangeCount > 0) {
      return res.status(400).json({ 
        message: '该职场关联了用户或订单数据，无法删除。请考虑将其设置为非活跃状态',
        userCount,
        exchangeCount
      });
    }

    // 记录职场数据用于日志
    const workplaceData = { ...workplace.get() };

    // 删除职场
    await workplace.destroy();

    // 记录日志
    await createLog({
      action: ACTIONS.WORKPLACE_DELETE,
      entityType: ENTITY_TYPES.WORKPLACE,
      entityId: id,
      oldValue: JSON.stringify(workplaceData),
      userId: req.user.id,
      username: req.user.username,
      description: `管理员删除了职场 "${workplaceData.name}"`
    }, req);

    return res.json({
      message: '职场删除成功'
    });
  } catch (error) {
    console.error('删除职场失败:', error);
    return res.status(500).json({ message: '删除职场失败，请稍后重试' });
  }
};

/**
 * 获取单个职场详情
 */
exports.getWorkplace = async (req, res) => {
  try {
    const { id } = req.params;

    const workplace = await Workplace.findByPk(id);
    if (!workplace) {
      return res.status(404).json({ message: '职场不存在' });
    }

    // 获取相关数据统计
    const userCount = await User.count({ where: { workplaceId: id } });
    const exchangeCount = await Exchange.count({ where: { workplaceId: id } });

    return res.json({
      workplace,
      stats: {
        userCount,
        exchangeCount
      }
    });
  } catch (error) {
    console.error('获取职场详情失败:', error);
    return res.status(500).json({ message: '获取职场详情失败，请稍后重试' });
  }
};

// 内部函数，创建日志条目
async function createLog(logData, req) {
  try {
    if (req && req.ip) {
      logData.ipAddress = req.ip;
    }

    if (req && req.headers && req.headers['user-agent']) {
      logData.userAgent = req.headers['user-agent'];
    }

    const log = await Log.create(logData);
    console.log('系统日志已创建:', log.id);
    return log;
  } catch (error) {
    console.error('创建系统日志失败:', error);
    // 这里不抛出异常，以免影响主要业务流程
    return null;
  }
} 