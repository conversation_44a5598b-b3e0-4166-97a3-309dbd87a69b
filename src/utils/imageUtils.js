/**
 * 图片URL处理工具函数
 * 用于处理不同环境下的图片URL，确保正确显示
 */

import { detectEnvironment, getEnvironmentBaseUrl, fixUrlForEnvironment } from './environmentDetector';

// 获取当前API基础URL
const getBaseUrl = () => {
  const env = detectEnvironment();
  // 强制返回正确的本地开发环境URL
  if (env.isLocalhost || env.isDevelopment) {
    return 'http://localhost:3000';
  }
  return getEnvironmentBaseUrl();
};

/**
 * 修复图片URL，确保能正确显示
 * 处理本地环境和生产环境的URL差异
 * 
 * @param {string} url - 原始图片URL
 * @returns {string} - 修复后的URL
 */
export const fixImageUrl = (url) => {
  if (!url) return '';
  
  console.log('fixImageUrl开始处理:', url);
  const env = detectEnvironment();
  const baseUrl = getBaseUrl();
  console.log('当前基础URL:', baseUrl);
  
  // 专门处理支付收款码图片路径
  if (url.includes('/uploads/payment/')) {
    console.log('检测到支付收款码URL路径');
    
    // 确保使用正确的环境域名
    if (env.isLocalhost && url.includes('**************')) {
      console.log('本地环境但使用了生产域名，修正支付码URL');
      // 从URL中提取路径部分
      const urlPath = url.replace(/^https?:\/\/[^\/]+/g, '');
      // 构建新的本地URL
      const localUrl = `http://localhost:3000${urlPath}`;
      console.log('修正后的支付码URL:', localUrl);
      return localUrl;
    }
  }
  
  // 首先尝试使用环境检测工具修正URL
  const envFixedUrl = fixUrlForEnvironment(url);
  if (envFixedUrl !== url) {
    console.log('环境检测工具修正URL:', url, '->', envFixedUrl);
    return envFixedUrl;
  }
  
  // 已经是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    console.log('URL已是完整URL，无需修改:', url);
    return url;
  }
  
  // 处理native-resource格式
  if (url.startsWith('native-resource://')) {
    const fixedUrl = url.replace('native-resource://', '/');
    console.log('处理native-resource格式:', url, '->', fixedUrl);
    return fixedUrl;
  }
  
  // 处理以localhost:3000开头的URL，替换为当前环境的baseUrl
  if (url.includes('localhost:3000')) {
    const fixedUrl = url.replace(/http:\/\/localhost:3000/g, baseUrl);
    console.log('处理localhost URL:', url, '->', fixedUrl);
    return fixedUrl;
  }
  
  // 处理支付码图片地址
  if (url.includes('/uploads/payment/')) {
    // 确保使用正确的基础URL
    const fixedUrl = `${baseUrl}${url.startsWith('/') ? '' : '/'}${url.replace(/^(https?:\/\/[^\/]+)?/, '')}`;
    console.log('处理支付码图片路径:', url, '->', fixedUrl);
    return fixedUrl;
  }
  
  // 处理相对路径，添加baseUrl
  if (url.startsWith('/')) {
    const fixedUrl = `${baseUrl}${url}`;
    console.log('处理绝对路径:', url, '->', fixedUrl);
    return fixedUrl;
  }
  
  // 其他情况，添加/前缀
  const fixedUrl = `/${url}`;
  console.log('处理其他情况:', url, '->', fixedUrl);
  return fixedUrl;
};

/**
 * 处理HTML内容中的图片URL
 * 
 * @param {string} html - HTML内容
 * @returns {string} - 处理后的HTML
 */
export const processHtmlImages = (html) => {
  if (!html) return '';
  
  try {
    // 替换所有img标签中的src属性
    return html.replace(/<img[^>]+src="([^"]+)"[^>]*>/g, (match, src) => {
      const fixedSrc = fixImageUrl(src);
      return match.replace(src, fixedSrc);
    });
  } catch (error) {
    console.error('处理HTML内容中的图片错误:', error);
    return html;
  }
};

export default {
  fixImageUrl,
  processHtmlImages
}; 