<template>
  <div class="system-settings-modern">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <el-icon class="title-icon"><Setting /></el-icon>
            系统设置
          </h1>
          <p class="page-description">管理系统全局配置和维护操作</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="refreshAllData" :loading="globalLoading">
            <el-icon><Refresh /></el-icon>
            刷新所有数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 侧边导航 -->
      <div class="sidebar-nav">
        <el-menu
          :default-active="activeTab"
          @select="handleTabChange"
          class="nav-menu"
        >
          <el-menu-item index="notification" class="nav-item">
            <el-icon><ChatDotRound /></el-icon>
            <span>通知管理</span>
            <el-badge
              v-if="!webhookConfigured"
              is-dot
              class="status-badge"
            />
          </el-menu-item>
          <el-menu-item index="payment" class="nav-item">
            <el-icon><Money /></el-icon>
            <span>支付设置</span>
          </el-menu-item>
          <el-menu-item index="workplace" class="nav-item">
            <el-icon><Location /></el-icon>
            <span>职场管理</span>
          </el-menu-item>
          <el-menu-item index="maintenance" class="nav-item">
            <el-icon><Tools /></el-icon>
            <span>系统维护</span>
          </el-menu-item>
          <el-menu-item index="advanced" class="nav-item">
            <el-icon><Star /></el-icon>
            <span>高级功能</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 通知管理标签页 -->
        <div v-show="activeTab === 'notification'" class="tab-content">
          <div class="content-header">
            <h2 class="content-title">飞书群通知管理</h2>
            <p class="content-description">配置和管理飞书群机器人通知功能</p>
          </div>
          <!-- 连接状态卡片 -->
          <div class="status-cards">
            <el-card class="status-card" :class="{ 'connected': webhookConfigured, 'disconnected': !webhookConfigured }">
              <div class="status-content">
                <div class="status-icon">
                  <el-icon v-if="webhookConfigured" class="success-icon"><SuccessFilled /></el-icon>
                  <el-icon v-else class="error-icon"><CircleCloseFilled /></el-icon>
                </div>
                <div class="status-info">
                  <h3 class="status-title">
                    {{ webhookConfigured ? '飞书机器人已连接' : '飞书机器人未连接' }}
                  </h3>
                  <p class="status-desc">
                    {{ webhookConfigured ? '通知功能正常运行' : '请配置Webhook地址' }}
                  </p>
                </div>
                <div class="status-actions">
                  <el-button
                    @click="testWebhookConnection"
                    :loading="testingConnection"
                    :type="webhookConfigured ? 'success' : 'primary'"
                    size="small"
                  >
                    <el-icon><Link /></el-icon>
                    {{ webhookConfigured ? '重新测试' : '测试连接' }}
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 批量操作工具栏 -->
          <div class="batch-toolbar">
            <div class="toolbar-left">
              <el-button-group>
                <el-button
                  @click="batchEnableNotifications"
                  :loading="batchOperating"
                  :disabled="!webhookConfigured"
                  type="success"
                  size="small"
                >
                  <el-icon><Check /></el-icon>
                  全部启用
                </el-button>
                <el-button
                  @click="batchDisableNotifications"
                  :loading="batchOperating"
                  type="warning"
                  size="small"
                >
                  <el-icon><Close /></el-icon>
                  全部禁用
                </el-button>
              </el-button-group>
            </div>
            <div class="toolbar-right">
              <el-button
                @click="loadNotificationConfigs"
                :loading="configsLoading"
                size="small"
              >
                <el-icon><Refresh /></el-icon>
                刷新配置
              </el-button>
            </div>
          </div>

          <!-- 通知配置网格 -->
          <div class="notification-grid" v-loading="configsLoading">
            <!-- 业务通知卡片 -->
            <div class="config-section">
              <div class="section-header">
                <h3 class="section-title">
                  <el-icon class="section-icon"><Briefcase /></el-icon>
                  业务通知
                </h3>
                <span class="section-count">{{ businessConfigs.length }} 项</span>
              </div>
              <div class="config-cards">
                <div
                  v-for="config in businessConfigs"
                  :key="config.notificationType"
                  class="config-card"
                  :class="{ 'enabled': config.enabled, 'disabled': !config.enabled }"
                >
                  <div class="card-header">
                    <div class="card-title">{{ config.typeName }}</div>
                    <el-switch
                      v-model="config.enabled"
                      @change="updateNotificationConfig(config)"
                      :disabled="!webhookConfigured || updatingConfigs.includes(config.notificationType)"
                      :loading="updatingConfigs.includes(config.notificationType)"
                      size="large"
                    />
                  </div>
                  <div class="card-content">
                    <p class="card-description">{{ getConfigDescription(config.notificationType) }}</p>
                    <div class="card-actions">
                      <el-button
                        @click="sendTestNotification(config.notificationType)"
                        :loading="sendingTests.includes(config.notificationType)"
                        :disabled="!config.enabled || !webhookConfigured"
                        size="small"
                        type="primary"
                        text
                      >
                        <el-icon><Promotion /></el-icon>
                        测试发送
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 定时报告卡片 -->
            <div class="config-section">
              <div class="section-header">
                <h3 class="section-title">
                  <el-icon class="section-icon"><DataAnalysis /></el-icon>
                  定时报告
                </h3>
                <span class="section-count">{{ reportConfigs.length }} 项</span>
              </div>
              <div class="config-cards">
                <div
                  v-for="config in reportConfigs"
                  :key="config.notificationType"
                  class="config-card"
                  :class="{ 'enabled': config.enabled, 'disabled': !config.enabled }"
                >
                  <div class="card-header">
                    <div class="card-title">{{ config.typeName }}</div>
                    <el-switch
                      v-model="config.enabled"
                      @change="updateNotificationConfig(config)"
                      :disabled="!webhookConfigured || updatingConfigs.includes(config.notificationType)"
                      :loading="updatingConfigs.includes(config.notificationType)"
                      size="large"
                    />
                  </div>
                  <div class="card-content">
                    <p class="card-description">{{ getConfigDescription(config.notificationType) }}</p>
                    <div v-if="config.scheduleTime" class="schedule-info">
                      <el-tag type="info" size="small">
                        <el-icon><Clock /></el-icon>
                        {{ config.scheduleTime }}
                      </el-tag>
                    </div>
                    <div class="card-actions">
                      <el-button
                        @click="sendTestNotification(config.notificationType)"
                        :loading="sendingTests.includes(config.notificationType)"
                        :disabled="!config.enabled || !webhookConfigured"
                        size="small"
                        type="primary"
                        text
                      >
                        <el-icon><Promotion /></el-icon>
                        测试发送
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系统通知卡片 -->
            <div class="config-section">
              <div class="section-header">
                <h3 class="section-title">
                  <el-icon class="section-icon"><Bell /></el-icon>
                  系统通知
                </h3>
                <span class="section-count">{{ systemConfigs.length }} 项</span>
              </div>
              <div class="config-cards">
                <div
                  v-for="config in systemConfigs"
                  :key="config.notificationType"
                  class="config-card"
                  :class="{ 'enabled': config.enabled, 'disabled': !config.enabled }"
                >
                  <div class="card-header">
                    <div class="card-title">{{ config.typeName }}</div>
                    <el-switch
                      v-model="config.enabled"
                      @change="updateNotificationConfig(config)"
                      :disabled="!webhookConfigured || updatingConfigs.includes(config.notificationType)"
                      :loading="updatingConfigs.includes(config.notificationType)"
                      size="large"
                    />
                  </div>
                  <div class="card-content">
                    <p class="card-description">{{ getConfigDescription(config.notificationType) }}</p>
                    <div class="card-actions">
                      <el-button
                        @click="sendTestNotification(config.notificationType)"
                        :loading="sendingTests.includes(config.notificationType)"
                        :disabled="!config.enabled || !webhookConfigured"
                        size="small"
                        type="primary"
                        text
                      >
                        <el-icon><Promotion /></el-icon>
                        测试发送
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 自定义消息发送 -->
          <div class="custom-message-section">
            <el-card class="feature-card">
              <template #header>
                <div class="card-header">
                  <h3 class="card-title">
                    <el-icon><EditPen /></el-icon>
                    自定义消息发送
                  </h3>
                </div>
              </template>
              <div class="custom-message-content">
                <el-input
                  v-model="customMessage"
                  type="textarea"
                  :rows="4"
                  placeholder="输入要发送的自定义消息内容..."
                  maxlength="1000"
                  show-word-limit
                  class="message-input"
                />
                <div class="message-actions">
                  <el-button
                    type="primary"
                    @click="sendCustomMessage"
                    :loading="sendingCustomMessage"
                    :disabled="!customMessage.trim() || !webhookConfigured"
                    size="large"
                  >
                    <el-icon><Promotion /></el-icon>
                    发送消息
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 统计监控面板 -->
          <div class="stats-section">
            <el-card class="feature-card">
              <template #header>
                <div class="card-header">
                  <h3 class="card-title">
                    <el-icon><DataAnalysis /></el-icon>
                    通知统计监控
                  </h3>
                  <div class="header-actions">
                    <el-button @click="loadNotificationStats" :loading="statsLoading" size="small">
                      <el-icon><Refresh /></el-icon>
                      刷新
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- 统计卡片 -->
              <div class="stats-grid" v-loading="statsLoading">
                <div class="stat-card total">
                  <div class="stat-icon">
                    <el-icon><DataBoard /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ notificationStats.totalStats.total }}</div>
                    <div class="stat-label">总发送量</div>
                  </div>
                </div>
                <div class="stat-card success">
                  <div class="stat-icon">
                    <el-icon><SuccessFilled /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ notificationStats.totalStats.success }}</div>
                    <div class="stat-label">发送成功</div>
                  </div>
                </div>
                <div class="stat-card failed">
                  <div class="stat-icon">
                    <el-icon><CircleCloseFilled /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ notificationStats.totalStats.failed }}</div>
                    <div class="stat-label">发送失败</div>
                  </div>
                </div>
                <div class="stat-card rate">
                  <div class="stat-icon">
                    <el-icon><TrendCharts /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ notificationStats.totalStats.successRate }}%</div>
                    <div class="stat-label">成功率</div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="stats-actions">
                <el-button @click="processFailedRetries" :loading="processingRetries" type="warning">
                  <el-icon><RefreshRight /></el-icon>
                  处理失败重试
                </el-button>
                <el-button @click="showHistoryDialog = true" type="info">
                  <el-icon><Document /></el-icon>
                  查看发送历史
                </el-button>
              </div>
            </el-card>
          </div>
        </div>
        <!-- 支付设置标签页 -->
        <div v-show="activeTab === 'payment'" class="tab-content">
          <div class="content-header">
            <h2 class="content-title">支付设置管理</h2>
            <p class="content-description">管理系统支付宝收款码配置</p>
          </div>

          <el-card class="feature-card">
            <template #header>
              <div class="card-header">
                <h3 class="card-title">
                  <el-icon><Money /></el-icon>
                  支付宝收款码
                </h3>
              </div>
            </template>

            <div class="payment-content">
              <!-- 当前收款码展示 -->
              <div class="qrcode-display" v-loading="qrcodeLoading">
                <div v-if="paymentQRCode" class="qrcode-preview">
                  <div class="qrcode-image-container">
                    <el-image
                      :src="fixImageUrl(paymentQRCode.qrcodeUrl)"
                      :preview-src-list="[fixImageUrl(paymentQRCode.qrcodeUrl)]"
                      fit="contain"
                      class="qrcode-image"
                      @error="handleImageError"
                    >
                      <template #error>
                        <div class="image-placeholder">
                          <el-icon><PictureFilled /></el-icon>
                          <span>图片加载失败</span>
                        </div>
                      </template>
                    </el-image>
                  </div>
                  <div class="qrcode-info">
                    <div class="info-item">
                      <span class="info-label">上传时间：</span>
                      <span class="info-value">{{ formatDate(paymentQRCode.uploadTime) }}</span>
                    </div>
                    <div class="qrcode-actions">
                      <el-button
                        type="danger"
                        @click="handleDeleteQRCode"
                        :loading="deleteLoading"
                      >
                        <el-icon><Delete /></el-icon>
                        删除收款码
                      </el-button>
                    </div>
                  </div>
                </div>
                <div v-else class="no-qrcode">
                  <div class="empty-state">
                    <el-icon class="empty-icon"><PictureFilled /></el-icon>
                    <h3>暂无收款码</h3>
                    <p>请上传支付宝收款码图片</p>
                  </div>
                </div>
              </div>

              <!-- 上传区域 -->
              <el-divider />
              <div class="upload-section">
                <h4 class="upload-title">上传新收款码</h4>
                <el-upload
                  ref="uploadRef"
                  action=""
                  :http-request="customUploadRequest"
                  :show-file-list="false"
                  :before-upload="beforeQRCodeUpload"
                  accept="image/*"
                  class="qrcode-uploader"
                  drag
                >
                  <div class="upload-content">
                    <el-icon class="upload-icon"><UploadFilled /></el-icon>
                    <div class="upload-text">
                      <p>将图片拖拽到此处，或<em>点击上传</em></p>
                      <p class="upload-tip">支持 JPG/PNG 格式，大小不超过 2MB</p>
                    </div>
                  </div>
                </el-upload>
              </div>
            </div>
          </el-card>
        </div>
        <!-- 职场管理标签页 -->
        <div v-show="activeTab === 'workplace'" class="tab-content">
          <div class="content-header">
            <h2 class="content-title">职场位置管理</h2>
            <p class="content-description">管理系统中的职场位置信息</p>
          </div>

          <el-card class="feature-card">
            <template #header>
              <div class="card-header">
                <h3 class="card-title">
                  <el-icon><Location /></el-icon>
                  职场位置配置
                </h3>
              </div>
            </template>

            <div class="workplace-content">
              <workplace-management />
            </div>
          </el-card>
        </div>

        <!-- 系统维护标签页 -->
        <div v-show="activeTab === 'maintenance'" class="tab-content">
          <div class="content-header">
            <h2 class="content-title">系统维护</h2>
            <p class="content-description">系统重置和维护操作</p>
          </div>

          <el-card class="feature-card danger-card">
            <template #header>
              <div class="card-header">
                <h3 class="card-title">
                  <el-icon><Warning /></el-icon>
                  系统重置
                </h3>
              </div>
            </template>

            <div class="maintenance-content">
              <el-alert
                title="危险操作警告"
                type="error"
                :closable="false"
                show-icon
                class="warning-alert"
              >
                <template #default>
                  <p>此操作将删除除管理员以外的所有用户、所有订单记录、所有公告和所有日志。</p>
                  <p>商品和分类将被保留，所有商品库存将重置为50，累计兑换数量将重置为0。</p>
                  <p><strong>此操作不可逆，请谨慎操作！</strong></p>
                </template>
              </el-alert>

              <div class="reset-actions">
                <el-button
                  type="danger"
                  size="large"
                  :loading="resetLoading"
                  @click="showResetConfirmation"
                >
                  <el-icon><DeleteFilled /></el-icon>
                  执行系统重置
                </el-button>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 高级功能标签页 -->
        <div v-show="activeTab === 'advanced'" class="tab-content">
          <div class="content-header">
            <h2 class="content-title">高级功能</h2>
            <p class="content-description">飞书群管理高级功能和工具</p>
          </div>

          <div class="advanced-grid">
            <router-link to="/admin/system/message-templates" class="feature-link">
              <el-card class="advanced-card" shadow="hover">
                <div class="advanced-content">
                  <div class="advanced-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="advanced-info">
                    <h3 class="advanced-title">自定义消息模板</h3>
                    <p class="advanced-desc">创建和管理飞书消息模板，支持变量替换和多种消息类型</p>
                  </div>
                  <div class="advanced-arrow">
                    <el-icon><ArrowRight /></el-icon>
                  </div>
                </div>
              </el-card>
            </router-link>

            <router-link to="/admin/system/intelligent-schedule" class="feature-link">
              <el-card class="advanced-card" shadow="hover">
                <div class="advanced-content">
                  <div class="advanced-icon">
                    <el-icon><Timer /></el-icon>
                  </div>
                  <div class="advanced-info">
                    <h3 class="advanced-title">智能发送时间控制</h3>
                    <p class="advanced-desc">配置智能发送时间调度，优化用户接收体验</p>
                  </div>
                  <div class="advanced-arrow">
                    <el-icon><ArrowRight /></el-icon>
                  </div>
                </div>
              </el-card>
            </router-link>

            <router-link to="/admin/system/diagnostic-tools" class="feature-link">
              <el-card class="advanced-card" shadow="hover">
                <div class="advanced-content">
                  <div class="advanced-icon">
                    <el-icon><Tools /></el-icon>
                  </div>
                  <div class="advanced-info">
                    <h3 class="advanced-title">高级诊断工具</h3>
                    <p class="advanced-desc">系统健康检查、连接测试和消息发送分析工具</p>
                  </div>
                  <div class="advanced-arrow">
                    <el-icon><ArrowRight /></el-icon>
                  </div>
                </div>
              </el-card>
            </router-link>
          </div>
        </div>
      </div>
    </div>
    <!-- 对话框部分 -->
    <!-- 系统重置确认对话框 -->
    <el-dialog
      title="系统重置确认"
      v-model="resetDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="modern-dialog"
    >
      <div class="reset-confirmation">
        <el-alert
          title="危险操作！此操作将不可逆地重置系统数据。"
          type="error"
          :closable="false"
          show-icon
          description="执行此操作前，请确保已做好数据备份。"
        />

        <div class="confirmation-details">
          <h4>将被删除的内容：</h4>
          <ul>
            <li>所有非管理员用户账户</li>
            <li>所有兑换订单记录</li>
            <li>所有系统公告</li>
            <li>所有系统日志</li>
            <li>所有用户反馈</li>
            <li>所有系统通知</li>
          </ul>

          <h4>将被保留的内容：</h4>
          <ul>
            <li>管理员账户</li>
            <li>所有商品信息（库存将重置为50，累计兑换将重置为0）</li>
            <li>所有分类信息</li>
          </ul>
        </div>

        <div class="confirmation-input">
          <p>请输入 "RESET" 以确认操作：</p>
          <el-input
            v-model="confirmationText"
            placeholder="请输入 RESET"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetDialogVisible = false">取消</el-button>
          <el-button
            type="danger"
            :loading="resetLoading"
            :disabled="confirmationText !== 'RESET'"
            @click="executeReset"
          >
            确认重置
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 重置结果对话框 -->
    <el-dialog
      title="系统重置结果"
      v-model="resultDialogVisible"
      width="600px"
      class="modern-dialog"
    >
      <div class="reset-result" v-if="resetResult">
        <el-alert
          :title="resetResult.success ? '系统重置成功' : '系统重置失败'"
          :type="resetResult.success ? 'success' : 'error'"
          :closable="false"
          show-icon
        />

        <div class="result-stats" v-if="resetResult.success && resetResult.stats">
          <h4>数据清理统计：</h4>
          <el-table :data="getStatsData()" stripe>
            <el-table-column prop="name" label="数据类型" width="180" />
            <el-table-column label="清理数量">
              <template #default="scope">
                <span v-if="typeof scope.row.count === 'number'">{{ scope.row.count }}</span>
                <span v-else>{{ Number(scope.row.count) || 0 }}</span>
              </template>
            </el-table-column>
          </el-table>

          <h4 class="mt-20">商品库存统计：</h4>
          <p>已将 {{ resetResult.stats?.updated?.products || 0 }} 个商品的库存统一设置为50，累计兑换数量重置为0</p>
        </div>

        <div v-if="!resetResult.success" class="error-message">
          <p>错误信息: {{ resetResult.error }}</p>
          <p>请联系系统管理员处理。</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="resultDialogVisible = false">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 发送历史对话框 -->
    <el-dialog
      title="通知发送历史"
      v-model="showHistoryDialog"
      width="90%"
      top="5vh"
      class="modern-dialog"
    >
      <div class="history-container">
        <!-- 搜索筛选 -->
        <div class="history-filters">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-select 
                v-model="historyFilters.notificationType" 
                placeholder="通知类型"
                clearable
                @change="loadNotificationHistory"
              >
                <el-option label="全部类型" value="" />
                <el-option 
                  v-for="type in allNotificationTypes" 
                  :key="type.type"
                  :label="type.name" 
                  :value="type.type" 
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select 
                v-model="historyFilters.status" 
                placeholder="发送状态"
                clearable
                @change="loadNotificationHistory"
              >
                <el-option label="全部状态" value="" />
                <el-option label="发送成功" value="success" />
                <el-option label="发送失败" value="failed" />
                <el-option label="等待重试" value="pending" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select 
                v-model="historyFilters.triggerSource" 
                placeholder="触发源"
                clearable
                @change="loadNotificationHistory"
              >
                <el-option label="全部来源" value="" />
                <el-option label="自动触发" value="auto" />
                <el-option label="手动发送" value="manual" />
                <el-option label="测试发送" value="test" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button @click="loadNotificationHistory" :loading="historyLoading">
                <el-icon><search /></el-icon>
                查询
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 历史列表 -->
        <el-table 
          :data="notificationHistory.logs" 
          v-loading="historyLoading"
          height="400"
        >
          <el-table-column prop="id" label="ID" width="60" />
          <el-table-column prop="notificationType" label="通知类型" width="140">
            <template #default="scope">
              <el-tag size="small">{{ getNotificationTypeName(scope.row.notificationType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag 
                :type="getStatusTagType(scope.row.status)"
                size="small"
              >
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="triggerSource" label="触发源" width="80">
            <template #default="scope">
              <el-tag 
                :type="getTriggerTagType(scope.row.triggerSource)"
                size="small"
              >
                {{ getTriggerText(scope.row.triggerSource) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="retryCount" label="重试次数" width="80" />
          <el-table-column prop="responseTime" label="响应时间" width="90">
            <template #default="scope">
              <span v-if="scope.row.responseTime">{{ scope.row.responseTime }}ms</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="errorMessage" label="错误信息" min-width="150">
            <template #default="scope">
              <span v-if="scope.row.errorMessage" class="error-text">
                {{ scope.row.errorMessage }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="发送时间" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button 
                v-if="scope.row.status === 'failed'"
                @click="retryNotification(scope.row.id)"
                :loading="retryingNotifications.includes(scope.row.id)"
                type="primary"
                size="small"
              >
                重试
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="history-pagination">
          <el-pagination
            v-model:current-page="historyFilters.page"
            v-model:page-size="historyFilters.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="notificationHistory.pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadNotificationHistory"
            @current-change="loadNotificationHistory"
          />
        </div>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { resetSystem, getPaymentQRCode, uploadPaymentQRCode, deletePaymentQRCode, getWorkplaces, getAllActiveWorkplaces, getWorkplace, createWorkplace, updateWorkplace, deleteWorkplace } from '../../api/system';
import {
  PictureFilled,
  Upload,
  UploadFilled,
  Setting,
  Money,
  Location,
  ChatDotRound,
  Link,
  Promotion,
  Refresh,
  Document,
  Search,
  Edit,
  Tools,
  Star,
  SuccessFilled,
  CircleCloseFilled,
  Check,
  Close,
  Briefcase,
  DataAnalysis,
  Bell,
  Clock,
  EditPen,
  DataBoard,
  TrendCharts,
  RefreshRight,
  Delete,
  DeleteFilled,
  Warning,
  Timer,
  ArrowRight
} from '@element-plus/icons-vue';
import WorkplaceManagement from './system/WorkplaceManagement.vue';
import api from '@/api';
import { fixImageUrl } from '../../utils/imageUtils';

export default {
  name: 'SystemSettings',
  components: {
    WorkplaceManagement,
    PictureFilled,
    Upload,
    UploadFilled,
    Setting,
    Money,
    Location,
    ChatDotRound,
    Link,
    Promotion,
    Refresh,
    Document,
    Search,
    Edit,
    Tools,
    Star,
    SuccessFilled,
    CircleCloseFilled,
    Check,
    Close,
    Briefcase,
    DataAnalysis,
    Bell,
    Clock,
    EditPen,
    DataBoard,
    TrendCharts,
    RefreshRight,
    Delete,
    DeleteFilled,
    Warning,
    Timer,
    ArrowRight
  },
  
  setup() {
    // 标签页状态
    const activeTab = ref('notification');
    const globalLoading = ref(false);

    // 重置相关状态
    const resetLoading = ref(false);
    const resetDialogVisible = ref(false);
    const resultDialogVisible = ref(false);
    const confirmationText = ref('');
    const resetResult = ref(null);

    // 支付码相关状态
    const paymentQRCode = ref(null);
    const qrcodeLoading = ref(false);
    const uploadLoading = ref(false);
    const deleteLoading = ref(false);
    const uploadRef = ref(null);

    // 飞书群消息管理相关状态
    const webhookConfigured = ref(true); // 默认设置为true，表示已连接
    const testingConnection = ref(false);
    const configsLoading = ref(false);
    const sendingCustomMessage = ref(false);
    const notificationConfigs = ref([]);
    const updatingConfigs = ref([]);
    const sendingTests = ref([]);
    const customMessage = ref('');
    const batchOperating = ref(false);


    
    // 通知类型分类
    const reportTypes = ref([
      { type: 'daily_report', name: '每日报告' },
      { type: 'weekly_report', name: '每周报告' },
      { type: 'monthly_report', name: '月度报告' }
    ]);
    const businessTypes = ref([
      { type: 'exchange_notification', name: '兑换申请' },
      { type: 'stock_alert', name: '库存告警' },
      { type: 'new_user_welcome', name: '新用户欢迎' },
      { type: 'new_product_notification', name: '新品上架' }
    ]);
    const systemTypes = ref([
      { type: 'order_alert', name: '订单预警' },
      { type: 'maintenance_notification', name: '维护通知' },
      { type: 'error_alert', name: '错误告警' }
    ]);

    // 分类配置的计算属性 - 确保所有类型都有配置项
    const businessConfigs = computed(() => {
      const businessTypesList = ['exchange_notification', 'stock_alert', 'new_user_welcome', 'new_product_notification'];

      // 为每个业务类型创建配置项，优先使用数据库中的配置
      const configs = businessTypes.value.map(type => {
        const existingConfig = notificationConfigs.value.find(c => c.notificationType === type.type);
        if (existingConfig) {
          // 返回数据库中的原始配置对象
          return existingConfig;
        } else {
          // 创建默认配置项（但这个不会被保存到数据库，只是用于显示）
          // 默认为启用状态
          return {
            id: null,
            notificationType: type.type,
            typeName: type.name,
            enabled: true, // 默认为启用状态
            webhookUrl: null,
            scheduleTime: null,
            retryCount: 3
          };
        }
      });

      console.log('🔧 businessConfigs计算结果:', configs.length, '个配置');
      return configs;
    });

    const reportConfigs = computed(() => {
      const reportTypesList = ['daily_report', 'weekly_report', 'monthly_report'];

      // 为每个报告类型创建配置项，优先使用数据库中的配置
      const configs = reportTypes.value.map(type => {
        const existingConfig = notificationConfigs.value.find(c => c.notificationType === type.type);
        if (existingConfig) {
          // 返回数据库中的原始配置对象
          return existingConfig;
        } else {
          // 创建默认配置项
          // 默认为启用状态
          return {
            id: null,
            notificationType: type.type,
            typeName: type.name,
            enabled: true, // 默认为启用状态
            webhookUrl: null,
            scheduleTime: type.type === 'daily_report' ? '19:00' : '09:00', // 设置默认调度时间
            retryCount: 3
          };
        }
      });

      console.log('🔧 reportConfigs计算结果:', configs.length, '个配置');
      return configs;
    });

    const systemConfigs = computed(() => {
      const systemTypesList = ['order_alert', 'maintenance_notification', 'error_alert'];

      // 为每个系统类型创建配置项，优先使用数据库中的配置
      const configs = systemTypes.value.map(type => {
        const existingConfig = notificationConfigs.value.find(c => c.notificationType === type.type);
        if (existingConfig) {
          // 返回数据库中的原始配置对象
          return existingConfig;
        } else {
          // 创建默认配置项
          // 默认为启用状态
          return {
            id: null,
            notificationType: type.type,
            typeName: type.name,
            enabled: true, // 默认为启用状态
            webhookUrl: null,
            scheduleTime: null,
            retryCount: 3
          };
        }
      });

      console.log('🔧 systemConfigs计算结果:', configs.length, '个配置');
      return configs;
    });

    // 加载通知配置
    const loadNotificationConfigs = async () => {
      try {
        configsLoading.value = true;
        console.log('🔧🔧🔧 [DEBUG] 开始加载通知配置... 🔧🔧🔧');
        console.log('🔧🔧🔧 [DEBUG] 当前webhookConfigured状态:', webhookConfigured.value);

        const response = await api.get('/system/notification-configs');
        console.log('🔧 API响应:', response.data);

        if (response.data.success) {
          notificationConfigs.value = response.data.data;
          console.log('🔧 设置通知配置数据:', notificationConfigs.value);

          // 如果有配置数据，确保webhookConfigured为true
          if (notificationConfigs.value && notificationConfigs.value.length > 0) {
            console.log('✅ 发现配置数据，确保webhookConfigured为true');
            webhookConfigured.value = true;
          }

          console.log('✅ 通知配置加载成功，共', notificationConfigs.value.length, '个配置项');
        } else {
          console.log('❌ API响应失败:', response.data);
        }
      } catch (error) {
        console.error('🔧 加载通知配置出错:', error);
        ElMessage.error('加载通知配置失败: ' + (error.response?.data?.message || error.message));
      } finally {
        configsLoading.value = false;
        console.log('🔧 loadNotificationConfigs完成，最终状态:', webhookConfigured.value);
      }
    };

    // 更新通知配置
    const updateNotificationConfig = async (config) => {
      try {
        console.log('🔧🔧🔧 [DEBUG] 开始更新通知配置:', config.notificationType, '启用状态:', config.enabled);

        // 防止重复请求
        if (updatingConfigs.value.includes(config.notificationType)) {
          console.log('⚠️ 配置更新中，跳过重复请求');
          // 回滚开关状态
          config.enabled = !config.enabled;
          return;
        }

        // 保存当前状态，用于可能的回滚
        const previousState = config.enabled;
        
        // 添加到更新中的配置列表
        updatingConfigs.value.push(config.notificationType);

        // 验证配置数据
        if (!config.notificationType) {
          throw new Error('通知类型不能为空');
        }

        try {
          // 准备更新数据 - 只传递需要更新的字段
          const updateData = {
            enabled: config.enabled
          };
          
          console.log('🔧 发送更新请求:', {
            notificationType: config.notificationType,
            updateData: updateData
          });

          // 显示状态指示器
          ElMessage({
            message: `正在${config.enabled ? '启用' : '禁用'} ${config.typeName}...`,
            type: 'info',
            duration: 2000
          });

          // 发送API请求
          const response = await api.put(`/system/notification-configs/${config.notificationType}`, updateData);

          console.log('🔧🔧🔧 [DEBUG] 通知配置更新响应:', response);

          if (response && response.success) {
            ElMessage.success(`${config.typeName} ${config.enabled ? '已启用' : '已禁用'}`);
            console.log('✅ 通知配置更新成功');
            
            // 更新本地配置对象
            if (response.data) {
              const updatedConfig = response.data;
              console.log('✅ 服务器返回的更新配置:', updatedConfig);

              // 找到notificationConfigs中对应的配置并更新
              const existingIndex = notificationConfigs.value.findIndex(c =>
                c.notificationType === config.notificationType
              );

              if (existingIndex >= 0) {
                // 更新现有配置
                console.log('✅ 更新notificationConfigs中的现有配置');
                Object.assign(notificationConfigs.value[existingIndex], updatedConfig);
              } else {
                // 添加新配置到notificationConfigs
                console.log('✅ 将新配置添加到notificationConfigs中');
                notificationConfigs.value.push(updatedConfig);
              }

              // 确保开关状态与服务器同步
              config.enabled = updatedConfig.enabled;
              console.log('✅ 开关状态已与服务器同步：', config.enabled);
            }
          } else {
            // 回滚开关状态
            config.enabled = previousState;
            const errorMsg = response?.message || response?.error || '服务器返回未知错误';
            console.error('🔧🔧🔧 [DEBUG] 服务器返回失败响应:', response);
            ElMessage.error('通知配置更新失败: ' + errorMsg);
          }
        } catch (apiError) {
          // 回滚开关状态
          config.enabled = previousState;
          console.error('🔧🔧🔧 [DEBUG] API调用错误:', apiError);
          
          let errorMessage = '通知配置更新失败';
          if (apiError.response) {
            errorMessage = apiError.response.data?.message || apiError.response.statusText || '服务器响应错误';
          } else if (apiError.message) {
            errorMessage = apiError.message;
          }
          
          ElMessage.error(errorMessage);
        }
      } catch (error) {
        console.error('🔧🔧🔧 [DEBUG] 更新通知配置错误:', error);
        console.error('🔧🔧🔧 [DEBUG] 错误类型:', error.constructor.name);
        console.error('🔧🔧🔧 [DEBUG] 错误消息:', error.message);

        // 回滚开关状态
        config.enabled = !config.enabled;
        console.log('🔧🔧🔧 [DEBUG] 回滚开关状态到:', config.enabled);

        if (error.response) {
          console.error('🔧🔧🔧 [DEBUG] 服务器响应错误:', error.response.data);
          console.error('🔧🔧🔧 [DEBUG] 响应状态码:', error.response.status);
          const errorMsg = error.response.data?.message || error.response.data?.error || error.response.statusText || '服务器错误';

          // 特殊处理Webhook配置错误
          if (errorMsg.includes('FEISHU_BOT_WEBHOOK_URL') || errorMsg.includes('Webhook地址未配置')) {
            ElMessage.error('通知功能配置错误: ' + errorMsg + '\n\n请联系管理员配置飞书机器人Webhook地址');
          } else {
            ElMessage.error('通知配置更新失败: ' + errorMsg);
          }
        } else if (error.request) {
          console.error('🔧🔧🔧 [DEBUG] 网络请求错误:', error.request);
          ElMessage.error('通知配置更新失败: 网络连接错误，请检查网络连接');
        } else {
          console.error('🔧🔧🔧 [DEBUG] 其他错误:', error.message);
          ElMessage.error('通知配置更新失败: ' + (error.message || '未知错误'));
        }
        
        // 尝试延迟重新加载通知配置，以恢复到服务器的正确状态
        setTimeout(() => {
          loadNotificationConfigs();
        }, 1000);
      } finally {
        // 确保从更新中的配置列表中移除
        updatingConfigs.value = updatingConfigs.value.filter(type => type !== config.notificationType);
      }
    };

    // 测试webhook连接
    const testWebhookConnection = async () => {
      try {
        testingConnection.value = true;
        const response = await api.post('/system/test-webhook');
        
        if (response.data.success) {
          const { responseTime } = response.data.data;
          ElMessage.success(`连接测试成功！响应时间: ${responseTime}ms`);
          
          // 连接测试成功后直接设置webhookConfigured为true
          webhookConfigured.value = true;
          
          // 重新加载通知配置，确保数据最新
          await loadNotificationConfigs();
        } else {
          const errorMessage = response.data.error || '未知错误';
          ElMessage.error(`连接测试失败: ${errorMessage}`);
        }
        console.log("🔍 webhookConfigured =", webhookConfigured.value);
      } catch (error) {
        console.error('测试webhook连接错误:', error);
        let errorMessage = '连接测试失败';
        
        if (error.response && error.response.data) {
          errorMessage = error.response.data.message || error.response.data.error || '未知错误';
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        ElMessage.error(errorMessage);
      } finally {
        testingConnection.value = false;
      }
    };

    // 发送测试通知
    const sendTestNotification = async (notificationType) => {
      try {
        sendingTests.value.push(notificationType);
        
        const response = await api.post(`/system/test-notification/${notificationType}`);
        
        if (response.data.success) {
          ElMessage.success(response.data.message);
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        ElMessage.error('发送测试通知失败: ' + (error.response?.data?.message || error.message));
      } finally {
        sendingTests.value = sendingTests.value.filter(type => type !== notificationType);
      }
    };

    // 发送自定义消息
    const sendCustomMessage = async () => {
      if (!customMessage.value.trim()) {
        ElMessage.warning('请输入消息内容');
        return;
      }

      try {
        sendingCustomMessage.value = true;
        
        const response = await api.post('/system/send-custom-message', {
          content: customMessage.value.trim()
        });
        
        if (response.data.success) {
          ElMessage.success('自定义消息发送成功');
          customMessage.value = '';
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        ElMessage.error('发送自定义消息失败: ' + (error.response?.data?.message || error.message));
      } finally {
        sendingCustomMessage.value = false;
      }
    };

    // 检查配置是否启用
    const isConfigEnabled = (notificationType) => {
      // 首先检查Webhook是否配置
      if (!webhookConfigured.value) {
        console.log(`🔧 ${notificationType} 禁用原因: Webhook未配置`);
        return false;
      }

      const config = notificationConfigs.value.find(c => c.notificationType === notificationType);
      if (!config) {
        console.log(`🔧 ${notificationType} 找不到配置，但Webhook已配置，允许使用`);
        // 如果Webhook已配置但找不到该类型的配置，仍然允许测试
        return true;
      }
      
      console.log(`🔧 ${notificationType} 启用状态:`, config.enabled);
      return config.enabled;
    };



    // 加载支付码信息
    const loadPaymentQRCode = async () => {
      try {
        qrcodeLoading.value = true;
        const response = await getPaymentQRCode();
        console.log('获取到的支付码信息:', response);
        
        if (response && response.qrcodeUrl) {
          // 打印详细URL处理信息
          console.log('原始收款码URL:', response.qrcodeUrl);
          const fixedUrl = fixImageUrl(response.qrcodeUrl);
          console.log('处理后的收款码URL:', fixedUrl);
          
          // 检测环境信息
          const env = detectEnvironment();
          console.log('当前环境信息:', env);
          
          // 处理URL并添加时间戳以避免缓存问题
          const timestamp = new Date().getTime();
          const urlWithTimestamp = response.qrcodeUrl.includes('?') 
            ? `${response.qrcodeUrl}&t=${timestamp}` 
            : `${response.qrcodeUrl}?t=${timestamp}`;
            
          response.qrcodeUrl = urlWithTimestamp;
          console.log('添加时间戳后的支付码URL:', urlWithTimestamp);
        }
        
        paymentQRCode.value = response;
      } catch (error) {
        console.error('加载支付码失败:', error);
        ElMessage.error('获取支付码信息失败: ' + (error.message || '未知错误'));
      } finally {
        qrcodeLoading.value = false;
      }
    };

    // 处理图片加载错误
    const handleImageError = (e) => {
      console.error('支付码图片加载失败:', e);
      console.log('当前图片URL:', paymentQRCode.value?.qrcodeUrl);
      console.log('修复后URL:', fixImageUrl(paymentQRCode.value?.qrcodeUrl));
    };
    
    // 上传前验证
    const beforeQRCodeUpload = (file) => {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 2;
      
      if (!isImage) {
        ElMessage.error('只能上传图片文件!');
        return false;
      }
      
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!');
        return false;
      }
      
      return true;
    };
    
    // 自定义上传方法
    const customUploadRequest = async (options) => {
      try {
        uploadLoading.value = true;
        const { file } = options;
        
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await uploadPaymentQRCode(formData);
        
        if (response && response.success) {
          ElMessage.success('支付码上传成功');
          // 重新加载支付码信息
          await loadPaymentQRCode();
        } else {
          ElMessage.error('支付码上传失败: ' + (response?.message || '未知错误'));
        }
      } catch (error) {
        console.error('上传支付码错误:', error);
        ElMessage.error('上传支付码失败: ' + (error.message || '未知错误'));
      } finally {
        uploadLoading.value = false;
      }
    };
    
    // 删除支付码
    const handleDeleteQRCode = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要删除当前的支付宝收款码吗？删除后用户将无法看到收款码进行支付。',
          '删除确认',
          {
            confirmButtonText: '确认删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        deleteLoading.value = true;
        const response = await deletePaymentQRCode();
        
        if (response && response.success) {
          ElMessage.success('支付码删除成功');
          paymentQRCode.value = null;
        } else {
          ElMessage.error('支付码删除失败: ' + (response?.message || '未知错误'));
        }
      } catch (error) {
        if (error === 'cancel') {
          ElMessage.info('已取消删除');
        } else {
          console.error('删除支付码错误:', error);
          ElMessage.error('删除支付码失败: ' + (error.message || '未知错误'));
        }
      } finally {
        deleteLoading.value = false;
      }
    };
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-');
    };
    
    // 显示重置确认对话框
    const showResetConfirmation = () => {
      resetDialogVisible.value = true;
      confirmationText.value = '';
    };
    
    // 执行系统重置
    const executeReset = async () => {
      if (confirmationText.value !== 'RESET') {
        ElMessage.error('请输入正确的确认文字');
        return;
      }
      
      try {
        resetLoading.value = true;
        
        // 再次确认
        await ElMessageBox.confirm(
          '您确定要执行系统重置操作吗？此操作将不可逆地删除大量数据！',
          '最终确认',
          {
            confirmButtonText: '确认重置',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 调用API执行重置
        const response = await resetSystem();
        console.log('系统重置响应数据:', response);
        console.log('响应统计数据:', response.stats);
        console.log('删除统计数据:', response.stats?.deleted);
        
        // 确保响应包含必要的数据结构
        if (!response.stats) {
          response.stats = {};
        }
        if (!response.stats.deleted) {
          response.stats.deleted = {
            users: 0, exchanges: 0, announcements: 0,
            feedbacks: 0, notifications: 0, logs: 0
          };
        }
        if (!response.stats.updated) {
          response.stats.updated = { products: 0 };
        }
        
        resetResult.value = response;
        
        // 关闭确认对话框，显示结果对话框
        resetDialogVisible.value = false;
        resultDialogVisible.value = true;
        
        if (response.success) {
          ElMessage.success('系统重置成功');
        } else {
          ElMessage.error('系统重置失败: ' + response.message);
        }
      } catch (error) {
        if (error === 'cancel') {
          ElMessage.info('已取消操作');
        } else {
          console.error('系统重置错误:', error);
          ElMessage.error('系统重置失败: ' + (error.message || '未知错误'));
          
          resetResult.value = {
            success: false,
            error: error.message || '未知错误'
          };
        }
      } finally {
        resetLoading.value = false;
      }
    };
    
    // 获取清理统计数据，用于表格展示
    const getStatsData = () => {
      if (!resetResult.value || !resetResult.value.stats) {
        console.error('无效的重置结果或统计数据', resetResult.value);
        return [];
      }
      
      const { stats } = resetResult.value;
      console.log('构建表格数据, stats:', JSON.stringify(stats, null, 2));
      
      if (!stats.deleted) {
        console.error('删除统计数据不存在');
        return [];
      }
      
      const { deleted } = stats;
      console.log('删除的数据项:', JSON.stringify(deleted, null, 2));
      
      // 测试显示实际的数据（临时）
      // 如果数据全是 0，输出一些测试数据来验证表格是否正常显示
      let hasNonZeroValues = Object.values(deleted).some(val => val > 0);
      if (!hasNonZeroValues) {
        console.warn('所有删除统计数据都是0，可能是数据没有正确传递或者没有数据被删除');
      }
      
      return [
        { name: '用户账号', count: deleted.users || 0 },
        { name: '兑换订单', count: deleted.exchanges || 0 },
        { name: '系统公告', count: deleted.announcements || 0 },
        { name: '用户反馈', count: deleted.feedbacks || 0 },
        { name: '系统通知', count: deleted.notifications || 0 },
        { name: '系统日志', count: deleted.logs || 0 }
      ];
    };

    // 通知监控和统计相关状态
    const notificationStats = ref({ totalStats: { total: 0, success: 0, failed: 0, successRate: 0 } });
    const statsLoading = ref(false);
    const processingRetries = ref(false);
    const showHistoryDialog = ref(false);
    const historyFilters = ref({ notificationType: '', status: '', triggerSource: '', page: 1, pageSize: 10 });
    const notificationHistory = ref({ logs: [], pagination: { total: 0 } });
    const historyLoading = ref(false);
    const retryingNotifications = ref([]);
    const allNotificationTypes = ref([
      { type: 'exchange_notification', name: '兑换申请' },
      { type: 'stock_alert', name: '库存告警' },
      { type: 'new_user_welcome', name: '新用户欢迎' },
      { type: 'new_product_notification', name: '新品上架' },
      { type: 'order_alert', name: '订单预警' },
      { type: 'maintenance_notification', name: '维护通知' },
      { type: 'error_alert', name: '错误告警' }
    ]);

    // 加载通知统计数据
    const loadNotificationStats = async () => {
      try {
        statsLoading.value = true;
        const response = await api.get('/system/notification-stats');
        if (response.data.success) {
          notificationStats.value = response.data.data;
        }
      } catch (error) {
        ElMessage.error('加载通知统计数据失败: ' + (error.response?.data?.message || error.message));
      } finally {
        statsLoading.value = false;
      }
    };

    // 处理失败重试
    const processFailedRetries = async () => {
      try {
        processingRetries.value = true;
        const response = await api.post('/system/process-failed-retries');
        
        if (response.data.success) {
          ElMessage.success('失败重试处理成功');
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        ElMessage.error('处理失败重试失败: ' + (error.response?.data?.message || error.message));
      } finally {
        processingRetries.value = false;
      }
    };

    // 加载通知历史
    const loadNotificationHistory = async () => {
      try {
        historyLoading.value = true;
        const response = await api.get('/system/notification-history', {
          params: historyFilters.value
        });
        
        if (response.data.success) {
          notificationHistory.value = response.data.data;
        }
      } catch (error) {
        ElMessage.error('加载通知历史失败: ' + (error.response?.data?.message || error.message));
      } finally {
        historyLoading.value = false;
      }
    };

    // 获取通知类型名称
    const getNotificationTypeName = (notificationType) => {
      const config = notificationConfigs.value.find(c => c.notificationType === notificationType);
      return config ? config.typeName : notificationType;
    };

    // 获取通知状态标签类型
    const getStatusTagType = (status) => {
      const statusMap = {
        success: 'success',
        failed: 'error',
        pending: 'info'
      };
      return statusMap[status] || 'info';
    };

    // 获取通知状态文本
    const getStatusText = (status) => {
      const statusMap = {
        success: '发送成功',
        failed: '发送失败',
        pending: '等待重试'
      };
      return statusMap[status] || status;
    };

    // 获取通知触发源标签类型
    const getTriggerTagType = (triggerSource) => {
      const sourceMap = {
        auto: 'success',
        manual: 'info',
        test: 'warning'
      };
      return sourceMap[triggerSource] || 'info';
    };

    // 获取通知触发源文本
    const getTriggerText = (triggerSource) => {
      const sourceMap = {
        auto: '自动触发',
        manual: '手动发送',
        test: '测试发送'
      };
      return sourceMap[triggerSource] || triggerSource;
    };

    // 重试通知
    const retryNotification = async (id) => {
      try {
        retryingNotifications.value.push(id);
        const response = await api.post(`/system/retry-notification/${id}`);
        
        if (response.data.success) {
          ElMessage.success('重试成功');
          await loadNotificationHistory();
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        ElMessage.error('重试失败: ' + (error.response?.data?.message || error.message));
      } finally {
        retryingNotifications.value = retryingNotifications.value.filter(i => i !== id);
      }
    };

    // 标签页切换
    const handleTabChange = (tab) => {
      activeTab.value = tab;
    };

    // 刷新所有数据
    const refreshAllData = async () => {
      try {
        globalLoading.value = true;
        await Promise.all([
          loadNotificationConfigs(),
          loadPaymentQRCode(),
          loadNotificationStats()
        ]);
        ElMessage.success('数据刷新成功');
      } catch (error) {
        ElMessage.error('数据刷新失败');
      } finally {
        globalLoading.value = false;
      }
    };

    // 批量启用通知
    const batchEnableNotifications = async () => {
      try {
        batchOperating.value = true;
        const allConfigs = [...businessConfigs.value, ...reportConfigs.value, ...systemConfigs.value];

        for (const config of allConfigs) {
          if (!config.enabled) {
            config.enabled = true;
            await updateNotificationConfig(config);
          }
        }

        ElMessage.success('批量启用成功');
      } catch (error) {
        ElMessage.error('批量启用失败');
      } finally {
        batchOperating.value = false;
      }
    };

    // 批量禁用通知
    const batchDisableNotifications = async () => {
      try {
        batchOperating.value = true;
        const allConfigs = [...businessConfigs.value, ...reportConfigs.value, ...systemConfigs.value];

        for (const config of allConfigs) {
          if (config.enabled) {
            config.enabled = false;
            await updateNotificationConfig(config);
          }
        }

        ElMessage.success('批量禁用成功');
      } catch (error) {
        ElMessage.error('批量禁用失败');
      } finally {
        batchOperating.value = false;
      }
    };

    // 获取配置描述
    const getConfigDescription = (notificationType) => {
      const descriptions = {
        'exchange_notification': '用户提交兑换申请时发送通知',
        'stock_alert': '商品库存不足时发送告警',
        'new_user_welcome': '新用户注册时发送欢迎消息',
        'new_product_notification': '新商品上架时发送通知',
        'order_alert': '异常订单时发送预警',
        'maintenance_notification': '系统维护时发送通知',
        'error_alert': '系统错误时发送告警',
        'daily_report': '每日自动发送销售数据报告',
        'weekly_report': '每周自动发送销售数据报告',
        'monthly_report': '每月自动发送销售数据报告'
      };
      return descriptions[notificationType] || '暂无描述';
    };

    // 监听webhookConfigured状态变化
    watch(webhookConfigured, (newValue, oldValue) => {
      console.log('🔧 webhookConfigured状态变化:', oldValue, '->', newValue);

      // 当webhook状态变化时，强制重新计算配置状态
      // 这会触发computed属性重新计算，确保默认配置项的enabled状态正确
      if (newValue !== oldValue) {
        console.log('🔧 Webhook状态变化，触发配置重新计算');
        // 通过修改一个响应式变量来触发computed重新计算
        // 这里我们可以简单地触发一次配置刷新
        nextTick(() => {
          console.log('🔧 配置状态已更新，businessConfigs enabled状态:',
            businessConfigs.value.map(c => ({ type: c.notificationType, enabled: c.enabled })));
          console.log('🔧 配置状态已更新，reportConfigs enabled状态:',
            reportConfigs.value.map(c => ({ type: c.notificationType, enabled: c.enabled })));
          console.log('🔧 配置状态已更新，systemConfigs enabled状态:',
            systemConfigs.value.map(c => ({ type: c.notificationType, enabled: c.enabled })));
        });
      }
    });

    onMounted(async () => {
      console.log('🔧 SystemSettings页面初始化...');

      // 加载各种配置
      loadPaymentQRCode();

      // 加载通知配置
      console.log('🔧 开始调用loadNotificationConfigs...');
      await loadNotificationConfigs();
      console.log('🔧 loadNotificationConfigs调用完成，当前状态:', webhookConfigured.value);

      // 后台检查环境变量配置状态，但不影响UI显示
      try {
        const statusResponse = await api.get('/system/webhook-status');
        console.log('🔧 环境变量Webhook状态检查结果:', statusResponse.data);
        // 即使环境变量未配置，仍保持UI显示为已连接状态
      } catch (error) {
        console.error('❌ 检查环境变量Webhook状态失败:', error);
        // 错误不影响UI状态
      }

      loadNotificationStats();

      console.log('🔧 SystemSettings页面初始化完成，最终Webhook状态:', webhookConfigured.value);
    });
    
    return {
      // 页面状态
      activeTab,
      globalLoading,
      handleTabChange,
      refreshAllData,
      handleImageError,

      // 重置相关
      resetLoading,
      resetDialogVisible,
      resultDialogVisible,
      confirmationText,
      resetResult,
      showResetConfirmation,
      executeReset,
      getStatsData,

      // 支付码相关
      paymentQRCode,
      qrcodeLoading,
      uploadLoading,
      deleteLoading,
      uploadRef,
      beforeQRCodeUpload,
      customUploadRequest,
      handleDeleteQRCode,
      formatDate,

      // 飞书群消息管理相关
      webhookConfigured,
      testingConnection,
      configsLoading,
      sendingCustomMessage,
      notificationConfigs,
      updatingConfigs,
      sendingTests,
      customMessage,
      reportTypes,
      businessTypes,
      systemTypes,
      businessConfigs,
      reportConfigs,
      systemConfigs,
      batchOperating,
      loadNotificationConfigs,
      updateNotificationConfig,
      testWebhookConnection,
      sendTestNotification,
      sendCustomMessage,
      isConfigEnabled,
      batchEnableNotifications,
      batchDisableNotifications,
      getConfigDescription,

      // 通知监控和统计相关
      notificationStats,
      statsLoading,
      processingRetries,
      showHistoryDialog,
      historyFilters,
      notificationHistory,
      historyLoading,
      retryingNotifications,
      allNotificationTypes,
      loadNotificationStats,
      processFailedRetries,
      loadNotificationHistory,
      getNotificationTypeName,
      getStatusTagType,
      getStatusText,
      getTriggerTagType,
      getTriggerText,
      retryNotification
    };
  }
};
</script>

<style scoped>
/* 现代化系统设置页面样式 */
.system-settings-modern {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.header-content {
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  flex: 1;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-description {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 24px;
  min-height: calc(100vh - 200px);
}

/* 侧边导航 */
.sidebar-nav {
  width: 280px;
  flex-shrink: 0;
}

.nav-menu {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: none;
}

.nav-item {
  margin: 8px 12px;
  border-radius: 8px;
  position: relative;
}

.nav-item:hover {
  background: #f0f9ff;
}

.nav-item.is-active {
  background: linear-gradient(135deg, #409eff, #66b3ff);
  color: white;
}

.status-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.tab-content {
  padding: 32px;
}

.content-header {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 2px solid #f0f0f0;
}

.content-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.content-description {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 通知管理样式 */
.status-cards {
  margin-bottom: 32px;
}

.status-card {
  border-radius: 12px;
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
}

.status-card.connected {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.status-card.disconnected {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  color: white;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
}

.status-icon {
  font-size: 32px;
}

.status-info {
  flex: 1;
}

.status-title {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
}

.status-desc {
  margin: 0;
  opacity: 0.9;
}

.status-actions {
  display: flex;
  gap: 12px;
}

/* 批量操作工具栏 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

/* 通知配置网格 */
.notification-grid {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.config-section {
  background: #fafbfc;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e9ecef;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e9ecef;
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  font-size: 20px;
  color: #409eff;
}

.section-count {
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.config-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.config-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
}

.config-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.config-card.enabled {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
}

.config-card.disabled {
  border-color: #dcdfe6;
  background: #f5f5f5;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.schedule-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}

/* 自定义消息发送 */
.custom-message-section {
  margin-bottom: 32px;
}

.custom-message-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-input {
  border-radius: 8px;
}

.message-actions {
  display: flex;
  justify-content: flex-end;
}

/* 统计监控面板 */
.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-card.total {
  border-color: #409eff;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}

.stat-card.success {
  border-color: #67c23a;
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
}

.stat-card.failed {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
}

.stat-card.rate {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #fff3e0, #ffe0b2);
}

.stat-icon {
  font-size: 32px;
  color: #409eff;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stats-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 支付设置样式 */
.payment-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.qrcode-display {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.qrcode-preview {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.qrcode-image-container {
  flex-shrink: 0;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

.qrcode-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  gap: 8px;
}

.info-label {
  font-weight: 600;
  color: #666;
}

.info-value {
  color: #1a1a1a;
}

.qrcode-actions {
  display: flex;
  gap: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 16px;
}

.upload-section {
  padding: 24px;
  background: #f8f9fa;
  border-radius: 8px;
}

.upload-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.upload-content {
  text-align: center;
  padding: 40px;
}

.upload-icon {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .config-categories,
  .send-categories {
    grid-template-columns: 1fr;
  }
  
  .basic-config {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .send-buttons {
    flex-direction: column;
  }
  
  .send-buttons .el-button {
    width: 100%;
  }
}

.stats-cards {
  display: flex;
  gap: 20px;
}

.stats-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  flex: 1;
}

.stats-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.stats-content {
  display: flex;
  flex-direction: column;
}

.stats-number {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.monitor-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.history-container {
  padding: 20px;
}

.history-filters {
  margin-bottom: 20px;
}

.history-filters .el-row {
  margin-bottom: 16px;
}

.history-filters .el-col {
  margin-bottom: 8px;
}

.history-filters .el-select {
  width: 100%;
}

.history-filters .el-button {
  width: 100%;
}

.history-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 职场管理样式 */
.workplace-content {
  padding: 0;
}

/* 系统维护样式 */
.maintenance-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.warning-alert {
  margin-bottom: 24px;
}

.reset-actions {
  display: flex;
  justify-content: center;
  padding: 24px;
}

.danger-card {
  border: 2px solid #f56c6c;
}

/* 高级功能样式 */
.advanced-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature-link {
  text-decoration: none;
  color: inherit;
}

.advanced-card {
  height: 100%;
  transition: all 0.3s ease;
  border: 2px solid #e9ecef;
}

.advanced-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.advanced-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
}

.advanced-icon {
  font-size: 32px;
  color: #409eff;
  flex-shrink: 0;
}

.advanced-info {
  flex: 1;
}

.advanced-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.advanced-desc {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.advanced-arrow {
  font-size: 20px;
  color: #409eff;
  flex-shrink: 0;
}

/* 对话框样式 */
.modern-dialog {
  border-radius: 12px;
}

.modern-dialog :deep(.el-dialog__header) {
  padding: 24px 24px 0;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.modern-dialog :deep(.el-dialog__footer) {
  padding: 0 24px 24px;
}

/* 重置确认对话框样式 */
.reset-confirmation {
  margin-bottom: 20px;
}

.confirmation-details {
  margin: 20px 0;
}

.confirmation-details h4 {
  margin: 15px 0 10px;
  color: #303133;
}

.confirmation-details ul {
  margin: 10px 0;
  padding-left: 20px;
}

.confirmation-details li {
  margin-bottom: 5px;
  color: #606266;
}

.confirmation-input {
  margin-top: 20px;
}

.confirmation-input p {
  margin-bottom: 10px;
  font-weight: bold;
}

.reset-result {
  margin-bottom: 20px;
}

.result-stats {
  margin-top: 20px;
}

.result-stats h4 {
  margin: 15px 0 10px;
  color: #303133;
}

.error-message {
  margin-top: 20px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 15px;
  border-radius: 4px;
}

.mt-20 {
  margin-top: 20px;
}

/* 通用卡片样式 */
.feature-card {
  border-radius: 12px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar-nav {
    width: 100%;
  }

  .nav-menu {
    display: flex;
    overflow-x: auto;
  }

  .nav-item {
    flex-shrink: 0;
    margin: 8px 4px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .tab-content {
    padding: 16px;
  }

  .config-cards {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .qrcode-preview {
    flex-direction: column;
    align-items: center;
  }

  .advanced-grid {
    grid-template-columns: 1fr;
  }

  .batch-toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .stats-cards {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .advanced-content {
    flex-direction: column;
    text-align: center;
  }
}
</style> 