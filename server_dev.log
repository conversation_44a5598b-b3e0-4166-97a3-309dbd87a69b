====== 服务器启动环境变量 ======
NODE_ENV: development
是否本地开发环境: 是
SERVER_URL (自动检测后): http://localhost:3000
FEISHU_REDIRECT_URI: http://localhost:3000/api/feishu/callback
====== 环境变量输出结束 ======
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
图片上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
支付码图片目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment
图片上传目录权限正常
静态文件路径1: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
静态文件路径2: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
支付码路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/payment
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
图片上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
上传目录已存在: /Users/<USER>/Desktop/chattywork/workyy/server/uploads
上传目录绝对路径: /Users/<USER>/Desktop/chattywork/workyy/server/uploads/images
=== 飞书配置加载 ===
NODE_ENV: development
FEISHU_REDIRECT_URI: http://localhost:3000/api/feishu/callback
process.env: {
  NODE_ENV: 'development',
  PORT: '3001',
  FEISHU_APP_ID: 'cli_a66b3b2dcab8d013',
  FEISHU_APP_SECRET: '5Fa8aatAGZ2Dv6K5VZhAWhbhjzE4lT2r',
  FEISHU_REDIRECT_URI: 'http://localhost:3000/api/feishu/callback'
}
计算得到的redirectUri: http://localhost:3000/api/feishu/callback
==================================
服务器启动成功，端口: 3001
API地址: http://localhost:3000/api
上传API: http://localhost:3000/api/upload
静态文件: http://localhost:3000/uploads
==================================
初始化定时报告服务...
✅ 定时报告服务初始化完成
📊 定时统计报告服务已启动
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: collate. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: acquireTimeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Ignoring invalid configuration option passed to Connection: timeout. This is currently a warning, but in future versions of MySQL2, an error will be thrown if you pass an invalid configuration option to a Connection
Executed (default): SELECT 1+1 AS result Elapsed time: 0ms
Database connection has been established successfully.
正在获取支付收款码信息...
读取到的收款码配置: {
  filename: 'qrcode_51d6bfc1-4d26-4901-b849-8ef9461327ca.png',
  uploadTime: '2025-06-05T08:45:28.518Z'
}
检查收款码文件 qrcode_51d6bfc1-4d26-4901-b849-8ef9461327ca.png 是否存在: true
收款码文件大小: 294304 字节
getQrcodeUrl: 环境=development, 本地开发=true
本地开发环境: 强制使用localhost URL
使用基础URL: http://localhost:3000，当前环境: development
生成的完整URL: http://localhost:3000/uploads/payment/qrcode_51d6bfc1-4d26-4901-b849-8ef9461327ca.png
生成的收款码URL: http://localhost:3000/uploads/payment/qrcode_51d6bfc1-4d26-4901-b849-8ef9461327ca.png
[0mGET /api/system/payment-qrcode [32m200[0m 7.470 ms - 202[0m
